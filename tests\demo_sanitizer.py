#!/usr/bin/env python3
"""
Demonstration script for the RenameWithEditor filename sanitizer.
Shows how invalid filenames are automatically fixed to prevent file destruction.
"""

import tempfile
import pathlib
import sys
import os

# Add src to path to import the main module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
from main import FileRenamer

def demo_sanitizer():
    """Demonstrate the filename sanitizer in action."""
    
    print("RenameWithEditor Filename Sanitizer Demo")
    print("=" * 45)
    print("This demo shows how invalid filenames are automatically fixed")
    print("instead of causing file destruction.\n")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = pathlib.Path(temp_dir)
        
        # Initialize the FileRenamer
        renamer = FileRenamer(temp_path)
        
        # Demo cases showing before/after sanitization
        demo_cases = [
            ("", "Empty filename"),
            ("CON.txt", "Windows reserved name"),
            ("file<name>.txt", "Invalid characters"),
            ("file:with|many?bad*chars.txt", "Multiple invalid characters"),
            ("a" * 300 + ".txt", "Extremely long filename"),
            ("normal_file.txt", "Valid filename (unchanged)"),
        ]
        
        print("Filename Sanitization Examples:")
        print("-" * 45)
        
        for original, description in demo_cases:
            if original:
                target_path = temp_path / original
            else:
                target_path = temp_path / " "  # Use space for empty test
                
            # Apply sanitization
            sanitized_path = renamer._sanitize_filename(target_path)
            sanitized_name = sanitized_path.name
            
            print(f"Description: {description}")
            print(f"  Before: '{original}'")
            print(f"  After:  '{sanitized_name}'")
            
            # Show what was fixed
            if original != sanitized_name:
                print(f"  Status: ✓ FIXED - Invalid filename automatically corrected")
            else:
                print(f"  Status: ✓ UNCHANGED - Valid filename passed through")
            print()
        
        print("Key Benefits:")
        print("- No file destruction from invalid filenames")
        print("- Operations succeed instead of failing")
        print("- User-friendly automatic corrections")
        print("- Comprehensive safety for all filesystem edge cases")
        print("\nThe sanitizer ensures your files are always safe!")

def demo_preview_integration():
    """Demonstrate how the preview shows sanitized filenames."""
    
    print("\n" + "=" * 60)
    print("PREVIEW INTEGRATION DEMO")
    print("=" * 60)
    print("This shows how the preview displays sanitized target filenames.\n")
    
    # Create a temporary directory for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = pathlib.Path(temp_dir)
        
        # Initialize the FileRenamer
        renamer = FileRenamer(temp_path)
        
        # Create test rename pairs with problematic target filenames
        test_rename_pairs = [
            ("normal_file.txt", "CON.txt"),  # Reserved name
            ("another_file.txt", "file<with>bad:chars.txt"),  # Invalid characters
            ("third_file.txt", "a" * 100 + ".txt"),  # Long filename
            ("fourth_file.txt", "valid_filename.txt"),  # Valid filename
        ]
        
        print("Original rename pairs (before sanitization):")
        for src, tgt in test_rename_pairs:
            print(f"  {src} → {tgt}")
        
        print("\nPreview output (with sanitized targets):")
        print("-" * 45)
        
        # Call the preview method which should show sanitized filenames
        renamer._preview_renames(test_rename_pairs)
        
        print("\nNotice how the preview shows the sanitized target filenames")
        print("that will actually be used during the rename operation.")

if __name__ == "__main__":
    demo_sanitizer()
    demo_preview_integration()
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETE")
    print("=" * 60)
    print("The RenameWithEditor utility now safely handles all filename edge cases!")
    print("Run 'python tests/run_tests.py' to execute the full test suite.")
