""" Utility for batch renaming files through an intermediary text editor (Class-based structure, Loguru-based logging). """

# SAFETY FEATURES IMPLEMENTED:
# - Automatic filename sanitization that fixes invalid filenames
# - Pre-validation testing before any file operations
# - Comprehensive filename safety checks and corrections
# - User-friendly approach that succeeds rather than fails

import argparse
import fnmatch
import hashlib
import os
import pathlib
import shutil
import subprocess
import sys
import re
import unicodedata
import time
import filetype
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

from dataclasses import dataclass
from enum import Enum
import tempfile
import json

# Rich imports for console interactions
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.table import Table
from rich.box import ROUNDED

# Loguru for logging
from loguru import logger


# =======================================================
# Configuration
# =======================================================
class Config:
    """
    Holds default settings for the Batch Rename Utility.
    """
    USE_DEFAULT_SETTINGS = True
    DEFAULT_CLEANUP_LOGS = True  # Temporarily set to False for debugging
    DEFAULT_INCLUDE_SUBDIRECTORIES = False
    DEFAULT_INCLUDE_TIMESTAMP = True
    DEFAULT_INCLUDE_DEPTH = True
    DEFAULT_INCLUDE_SIZE = True
    DEFAULT_INCLUDE_CONTENT = False
    DEFAULT_CONTENT_PREVIEW_LENGTH = 1500

    EXCLUDED_DIRS = [
        ".git", ".github", ".venv", ".versions",
        "__pycache__", "__tmp__", "node_modules", "venv",
        # ".backups",
    ]

    EXCLUDED_PATTERNS = [
        "*.pyc", "*.sublime-workspace", "*.log.yml", ".gitignore",
    ]

    # Binary file extensions to exclude from content preview (for performance)
    BINARY_FILE_EXTENSIONS = {
        # Video files
        '.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp',
        '.mpg', '.mpeg', '.m2v', '.mts', '.ts', '.vob', '.ogv',
        # Audio files
        '.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma', '.m4a', '.opus', '.aiff',
        '.au', '.ra', '.amr', '.ac3', '.dts',
        # Image files
        '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp', '.svg',
        '.ico', '.psd', '.ai', '.eps', '.raw', '.cr2', '.nef', '.arw', '.dng',
        '.heic', '.heif', '.avif',
        # Archive files
        '.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz', '.lzma', '.cab',
        '.iso', '.dmg', '.pkg', '.deb', '.rpm',
        # Executable files
        '.exe', '.dll', '.so', '.dylib', '.app', '.msi', '.bin', '.run',
        # Document files (binary formats)
        '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.odt',
        '.ods', '.odp', '.rtf',
        # Database files
        '.db', '.sqlite', '.sqlite3', '.mdb', '.accdb',
        # Font files
        '.ttf', '.otf', '.woff', '.woff2', '.eot',
        # Other binary formats
        '.swf', '.fla', '.blend', '.max', '.obj', '.fbx', '.dae', '.3ds',
        '.sketch', '.fig', '.xd'
    }

    # Large file optimization settings
    LARGE_FILE_THRESHOLD_MB = 100  # Files larger than this use fast hashing
    FAST_HASH_SAMPLE_SIZE = 8192   # Number of bytes to sample from start/end of large files


# =======================================================
# Logger Setup
# =======================================================
class LoggerSetup:
    """Sets up Loguru logger to write YAML logs to a file."""

    @staticmethod
    def setup_yaml_logging(log_file: str = "app.log.yml", level: str = "INFO"):
        """
        Configure Loguru to log messages in YAML format to `log_file`.
        """
        def yaml_sink(message):
            record = message.record

            time_str = record['time'].strftime('%Y-%m-%d %H:%M:%S')
            level_str = f"!{record['level'].name}"
            name_str = record['name']
            func_name_str = f"*{record['function']}"
            line_no = record["line"]
            msg = record["message"]

            # For multi-line messages, use a block scalar in YAML
            if "\n" in msg:
                lines = msg.split("\n")
                message_str = "|\n" + "\n".join(f"  {line}" for line in lines)
            else:
                # Quote if it has special characters like ':'
                if ":" in msg:
                    message_str = f"'{msg}'"
                else:
                    message_str = msg

            yaml_lines = [
                f"- time: {time_str}",
                f"  level: {level_str}",
                f"  name: {name_str}",
                f"  funcName: {func_name_str}",
                f"  lineno: {line_no}",
                f"  message: {message_str}",
                ""
            ]

            with open(log_file, "a", encoding="utf-8") as f:
                f.write("\n".join(yaml_lines) + "\n")

        # Remove default handlers and add our YAML sink
        logger.remove()
        logger.add(yaml_sink, level=level, enqueue=True)

    @staticmethod
    def initialize_logging(verbosity: str = "INFO"):
        """
        Initialize YAML logging with a level mapped from the string `verbosity`.
        Maps:
          quiet -> ERROR
          normal -> INFO
          verbose -> DEBUG
          debug -> DEBUG
        """
        level_map = {
            "quiet": "ERROR",
            "normal": "INFO",
            "verbose": "DEBUG",
            "debug": "DEBUG"
        }
        selected_level = level_map.get(verbosity.lower(), "INFO")
        LoggerSetup.setup_yaml_logging(level=selected_level)


# =======================================================
# Argument Handler
# =======================================================
class ArgumentHandler:
    """Handles CLI argument parsing and optional prompting."""

    def __init__(self):
        self.parser = self.parse_arguments()

    @staticmethod
    def parse_arguments():
        logger.debug("Setting up argument parser.")
        parser = argparse.ArgumentParser(
            description="Batch Rename Utility with SHA256 Verification (using Loguru + Rich)."
        )

        parser.add_argument('-d', '--directory', type=str, help="Target directory for processing")

        parser.add_argument('--include_subdirs', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SUBDIRECTORIES,
                            help="Include subdirectories in file processing")

        parser.add_argument('--include-depth', action='store_true',
                            default=Config.DEFAULT_INCLUDE_DEPTH,
                            help="Include a folder depth column (integer).")

        parser.add_argument('--include-size', action='store_true',
                            default=Config.DEFAULT_INCLUDE_SIZE,
                            help="Include a file size column in KB.")

        parser.add_argument('--include-time', action='store_true',
                            default=Config.DEFAULT_INCLUDE_TIMESTAMP,
                            help="Include 'HH:MM' in the date column (e.g. YYYY.MM.DD HH:MM).")

        parser.add_argument('--include-content', action='store_true',
                            default=Config.DEFAULT_INCLUDE_CONTENT,
                            help="Include a preview of text file content (not applied to binary files).")

        parser.add_argument('--content-length', type=int,
                           default=Config.DEFAULT_CONTENT_PREVIEW_LENGTH,
                           help=f"Maximum length of content preview (default: {Config.DEFAULT_CONTENT_PREVIEW_LENGTH} chars).")

        parser.add_argument('--prompt', action='store_true', help="Prompt for missing arguments")

        parser.add_argument('-v', '--verbosity',
                            choices=["quiet", "normal", "verbose", "debug"],
                            default="normal",
                            help="Set output verbosity level")

        # Log Cleanup arguments
        cleanup_logs_group = parser.add_mutually_exclusive_group()
        cleanup_logs_group.add_argument('--cleanup-logs', dest='cleanup_logs', action='store_true',
                                        help="Clean up log files after successful execution (never deletes if errors occurred)")
        cleanup_logs_group.add_argument('--no-cleanup-logs', dest='cleanup_logs', action='store_false',
                                        help="Do not clean up log files after execution")
        parser.set_defaults(cleanup_logs=Config.DEFAULT_CLEANUP_LOGS)

        logger.debug("Argument parser setup complete.")
        return parser

    def get_arguments(self):
        return self.parser.parse_args()

    def prompt_for_missing_arguments(self, args):
        logger.debug("Prompting for missing arguments.")
        console = Console()

        def print_section(title):
            console.print(f"\n[bold blue] --- {title} ---[/bold blue]", highlight=False)

        if args.prompt:
            print_section("Default Settings")
            use_defaults = Confirm.ask("Use default settings?", default=Config.USE_DEFAULT_SETTINGS)
            logger.debug(f"Use defaults: {use_defaults}")

            if not use_defaults:
                # Prompt for input directory
                print_section("Directory")
                current_dir = args.directory or ""
                args.directory = Prompt.ask("Target directory path?", default=current_dir).strip()

                # Prompt for including subdirectories
                print_section("Include Subdirectories?")
                args.include_subdirs = Confirm.ask("Include subdirectories?", default=True)

                # Prompt for including depth
                print_section("Include Folder Depth?")
                args.include_depth = Confirm.ask("Include folder depth column?", default=True)

                # Prompt for including size
                print_section("Include File Size?")
                args.include_size = Confirm.ask("Include file size column in KB?", default=True)

                # Prompt for including timestamps
                print_section("Include Time?")
                args.include_time = Confirm.ask("Include HH:MM in the date column?", default=True)

                # Prompt for including content
                print_section("Include Content Preview?")
                args.include_content = Confirm.ask("Include a preview of text file content?", default=False)

                # Prompt for content length
                if args.include_content:
                    print_section("Content Preview Length")
                    args.content_length = int(Prompt.ask("Maximum length of content preview (chars)?", default=Config.DEFAULT_CONTENT_PREVIEW_LENGTH))

                # Prompt for logging verbosity
                print_section("Logging Verbosity")
                choices = ["quiet", "normal", "verbose", "debug"]
                console.print("Verbosity levels:\n  quiet\n  normal\n  verbose\n  debug\n")
                chosen_verbosity = Prompt.ask("Choose verbosity", default=args.verbosity, choices=choices)
                args.verbosity = chosen_verbosity

                # Prompt for cleanup logs
                print_section("Log Cleanup")
                cleanup_logs_default = False if chosen_verbosity in ["verbose", "debug"] else args.cleanup_logs
                args.cleanup_logs = Confirm.ask("Clean up log file after successful execution (never deletes if errors occurred)?", default=cleanup_logs_default)
            else:
                # Assign defaults if none provided
                args.directory = args.directory or ""
                args.include_subdirs = args.include_subdirs if args.include_subdirs is not None else Config.DEFAULT_INCLUDE_SUBDIRECTORIES
                args.include_size = args.include_size if args.include_size is not None else Config.DEFAULT_INCLUDE_SIZE
                args.include_content = args.include_content if args.include_content is not None else Config.DEFAULT_INCLUDE_CONTENT
                args.content_length = args.content_length if args.content_length is not None else Config.DEFAULT_CONTENT_PREVIEW_LENGTH
                # Let the existing defaults stand for verbosity, cleanup_logs, etc.

        # Validation
        if not args.directory:
            console.print("[red]Error:[/] The following argument is required: directory")
            sys.exit(1)

        dir_path = Path(args.directory)
        if not dir_path.exists() or not dir_path.is_dir():
            console.print(f"[red]Error:[/] Directory '{args.directory}' does not exist or is not a directory.")
            sys.exit(1)

        logger.debug("Argument prompting complete.")
        return args


# =======================================================
# File Hasher
# =======================================================
class FileHasher:
    """Computes SHA256 hashes for files with optimization for large files."""
    CHUNK_SIZE = 4096

    @staticmethod
    def compute_sha256(file_path: pathlib.Path) -> Optional[str]:
        """
        Compute hash for a file. Uses fast hashing for large files to improve performance.
        """
        try:
            file_size = file_path.stat().st_size
            size_mb = file_size / (1024 * 1024)

            # Use fast hashing for large files
            if size_mb > Config.LARGE_FILE_THRESHOLD_MB:
                logger.debug(f"Using fast hash for large file: {file_path.name} ({size_mb:.1f}MB)")
                return FileHasher._compute_fast_hash(file_path, file_size)
            else:
                # Use full SHA256 for smaller files
                return FileHasher._compute_full_sha256(file_path)

        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None

    @staticmethod
    def _compute_full_sha256(file_path: pathlib.Path) -> Optional[str]:
        """Compute full SHA256 hash by reading the entire file."""
        sha256 = hashlib.sha256()
        try:
            with file_path.open('rb') as f:
                for chunk in iter(lambda: f.read(FileHasher.CHUNK_SIZE), b''):
                    sha256.update(chunk)
            return sha256.hexdigest()
        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None

    @staticmethod
    def _compute_fast_hash(file_path: pathlib.Path, file_size: int) -> Optional[str]:
        """
        Compute a fast hash for large files by sampling start/end and using metadata.
        This creates a unique identifier that's much faster than full SHA256.
        """
        try:
            sha256 = hashlib.sha256()

            # Include file metadata in hash
            stat = file_path.stat()
            metadata = f"{file_path.name}|{file_size}|{stat.st_mtime}".encode('utf-8')
            sha256.update(metadata)

            sample_size = Config.FAST_HASH_SAMPLE_SIZE

            with file_path.open('rb') as f:
                # Read from the beginning
                start_data = f.read(sample_size)
                sha256.update(start_data)

                # Read from the end (if file is large enough)
                if file_size > sample_size * 2:
                    f.seek(-sample_size, 2)  # Seek to end minus sample_size
                    end_data = f.read(sample_size)
                    sha256.update(end_data)

                # Read from the middle (if file is large enough)
                if file_size > sample_size * 4:
                    middle_pos = file_size // 2
                    f.seek(middle_pos - sample_size // 2)
                    middle_data = f.read(sample_size)
                    sha256.update(middle_data)

            # Add a prefix to distinguish fast hashes from full hashes
            return "FAST_" + sha256.hexdigest()

        except IOError as error:
            logger.error(f"Error reading `{file_path}`: {error}")
            return None


# =======================================================
# File Processor
# =======================================================
class FileProcessor:
    """
    Processes files to collect their hashes, plus optional date/time + optional depth.
    Excludes any directories in Config.EXCLUDED_DIRS and any files matching Config.EXCLUDED_PATTERNS.
    """

    def __init__(
        self,
        root_dir: pathlib.Path,
        include_subdirs: bool,
        include_time: bool = False,
        include_depth: bool = False,
        include_size: bool = False,
        include_content: bool = False,
        content_length: int = 300
    ):
        self.root_dir = root_dir
        self.include_subdirs = include_subdirs
        self.include_time = include_time
        self.include_depth = include_depth
        self.include_size = include_size
        self.include_content = include_content
        self.content_length = content_length

        # Prepare sets/lists for exclusions
        self.excluded_dirs = set(Config.EXCLUDED_DIRS)
        self.excluded_patterns = Config.EXCLUDED_PATTERNS

    def collect_file_hashes(self) -> List[Tuple[str, str, str, str, str, str]]:
        """
        Collect a list of (file_hash, relative_filename, date_str, depth_str, size_str, content_str).

        - date_str: "YYYY.MM.DD" or "YYYY.MM.DD HH:MM"
        - depth_str: the folder depth with "lvl." prefix, or "" if not used
        - size_str: the file size in KB with ".kb" suffix, or "" if not used
        - content_str: minified preview of text file content, or "" if not used/applicable

        We skip directories in Config.EXCLUDED_DIRS and files matching any pattern in Config.EXCLUDED_PATTERNS.
        """
        # First pass: collect files and their sizes to determine max size (for padding calculation)
        file_info = []
        max_size_kb = 0

        # Use os.walk to gather files
        for root, dirs, files in os.walk(self.root_dir):
            # 1) remove excluded dirs from the walk
            dirs[:] = [d for d in dirs if d not in self.excluded_dirs]

            for filename in files:
                # 2) skip if matches any excluded pattern
                if any(fnmatch.fnmatch(filename, pat) for pat in self.excluded_patterns):
                    continue

                file_path = pathlib.Path(root) / filename
                if not self._is_accessible_file(file_path):
                    continue

                # Record file information and keep track of max size
                if self.include_size:
                    size_bytes = file_path.stat().st_size
                    size_kb = int(round(size_bytes / 1024))  # Integer KB size
                    max_size_kb = max(max_size_kb, size_kb)

                file_info.append((file_path, root))

            if not self.include_subdirs:
                # If user doesn't want subdirectories, stop after top-level
                break

        # Calculate padding digits needed based on max file size
        padding_digits = len(str(max_size_kb)) if max_size_kb > 0 else 1

        # Second pass: create the actual hash entries with proper formatting
        hash_entries: List[Tuple[str, str, str, str, str, str]] = []
        for file_path, root in file_info:
            filename = file_path.name
            relative_path = file_path.relative_to(self.root_dir).as_posix()
            file_hash = FileHasher.compute_sha256(file_path)

            if file_hash:
                # Build date/time
                mtime = file_path.stat().st_mtime
                if self.include_time:
                    date_str = time.strftime("%Y.%m.%d %H:%M", time.localtime(mtime))
                else:
                    date_str = time.strftime("%Y.%m.%d", time.localtime(mtime))

                # Build depth
                if self.include_depth:
                    depth_val = len(Path(relative_path).parts)
                    depth_str = f"lvl.{depth_val}"
                else:
                    depth_str = ""

                # Build size in KB
                if self.include_size:
                    size_bytes = file_path.stat().st_size
                    size_kb = int(round(size_bytes / 1024))  # Integer KB size
                    # Format with leading zeros based on the max file size
                    size_str = f"{size_kb:0{padding_digits}d}.kb"
                else:
                    size_str = ""

                # Get content preview if needed
                content_str = self._get_content_preview(file_path) if self.include_content else ""

                hash_entries.append((file_hash, relative_path, date_str, depth_str, size_str, content_str))
                logger.debug(f"Processed: {relative_path}")

        return hash_entries

    def _is_accessible_file(self, path: pathlib.Path) -> bool:
        if not path.is_file() or not os.access(path, os.R_OK):
            logger.warning(f"`{path}` is not accessible or not a file")
            return False
        return True

    def _is_text_file(self, file_path: pathlib.Path) -> bool:
        """
        Determine if a file is a text file (not a binary file).
        Uses a fast pre-filtering step based on file extensions, then falls back to content analysis.
        """
        try:
            # FAST PRE-FILTER: Check file extension first to quickly exclude known binary types
            file_extension = file_path.suffix.lower()
            if file_extension in Config.BINARY_FILE_EXTENSIONS:
                logger.debug(f"Skipping content check for {file_path.name} (known binary extension: {file_extension})")
                return False

            # Check file size - skip very large files
            if file_path.stat().st_size > 1024 * 1024:  # Skip files larger than 1MB
                return False

            # Use filetype to detect kind
            kind = filetype.guess(str(file_path))
            if kind is not None:
                # If filetype can detect a type, it's likely a binary file
                return False

            # As a fallback, try to read the first few bytes and check for binary content
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                textchars = bytearray({7, 8, 9, 10, 12, 13, 27} | set(range(0x20, 0x7F)) | set(range(0x80, 0x100)))
                return bool(chunk) and not bool(chunk.translate(None, textchars))
        except (IOError, OSError, UnicodeDecodeError):
            # If any error occurs, consider it not a text file
            return False

    def _get_content_preview(self, file_path: pathlib.Path) -> str:
        """
        Gets a minified content preview of a text file.
        Returns empty string for binary files or if reading fails.
        """
        if not self.include_content or not self._is_text_file(file_path):
            return ""

        try:
            with open(file_path, 'r', encoding='utf-8', errors='replace') as f:
                content = f.read()
                return TextMinifier.minify_text(content, self.content_length)
        except (IOError, OSError, UnicodeDecodeError) as error:
            logger.warning(f"Failed to read content from {file_path}: {error}")
            return ""


# =======================================================
# Hash File Manager
# =======================================================
class HashFileManager:
    """
    Manages reading and writing hash files with optional presentation elements:
    - If 'include_time' -> add date/time to lines.
    - If 'include_depth' -> add a DEPTH column.
    - If 'include_size' -> add a SIZE_KB column.
    - if 'include_content' -> add a CONTENT preview column.
    """

    def __init__(self, file_path: pathlib.Path, include_time: bool = False, include_depth: bool = False, include_size: bool = False, include_content: bool = False):
        self.file_path = file_path
        self.include_time = include_time
        self.include_depth = include_depth
        self.include_size = include_size
        self.include_content = include_content

    def write(self, hash_entries: List[Tuple[str, str, str, str, str, str]]) -> None:
        """
        Writes lines in any of these formats for the header:
          #  YYYY.MM.DD  | FILENAME                       | FILEHASH
          #  YYYY.MM.DD  HH:MM | FILENAME                 | FILEHASH
          #  YYYY.MM.DD  | DEPTH | FILENAME               | FILEHASH
          #  YYYY.MM.DD  HH:MM | DEPTH | FILENAME         | FILEHASH
          #  YYYY.MM.DD  | DEPTH | SIZE(KB) | FILENAME     | FILEHASH
          #  YYYY.MM.DD  HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH
          #  YYYY.MM.DD  | DEPTH | SIZE(KB) | FILENAME     | FILEHASH | CONTENT
          #  YYYY.MM.DD  HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH | CONTENT

        Each data line:
          ''' 2025.03.07 | ''' - "filename.txt" # | 'abc123...'
          ''' 2025.03.07 21:13 | 3 | ''' - "filename.txt" # | 'abc123...'
          ''' 2025.03.07 21:13 | lvl.3 | 42.kb | ''' - "filename.txt" # | 'abc123...' | ''' content preview '''
        """
        try:
            with self.file_path.open("w", encoding='utf-8') as f:
                # Build the header line
                header = self._build_header_line()
                f.write(header + "\n")

                # Sort by filename (case-insensitive)
                sorted_entries = sorted(hash_entries, key=lambda x: x[1].lower())

                # Determine padding for the filename column
                max_length = 2 + max((len(filename) for _, filename, _, _, _, _ in sorted_entries), default=0)

                for file_hash, filename, date_str, depth_str, size_str, content_str in sorted_entries:
                    # Construct the date/depth portion in the line
                    line = self._build_data_line(file_hash, filename, date_str, depth_str, size_str, content_str, max_length)
                    f.write(line)

            logger.info(f"Hash file written: {self.file_path.name}")
        except IOError as error:
            logger.error(f"Failed to write hash file: {error}")

    def _build_header_line(self) -> str:
        """Return the appropriate header line based on include_time / include_depth / include_size / include_content."""
        # Essential elements for renaming are "FILENAME" and "FILEHASH" - formatting can be freely modified
        if self.include_time and self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH | CONTENT"
        elif self.include_time and self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH
            return "# YYYY.MM.DD HH:MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH"
        elif self.include_time and self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD HH:MM | DEPTH | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD HH:MM | DEPTH | FILENAME | FILEHASH | CONTENT"
        elif self.include_time and self.include_depth and not self.include_size and not self.include_content:
            # #  YYYY.MM.DD HH:MM | DEPTH | FILENAME | FILEHASH
            return "# YYYY.MM.DD HH:MM | DEPTH | FILENAME | FILEHASH"
        elif self.include_time and not self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD HH:MM | SIZE(KB) | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD HH:MM | SIZE(KB) | FILENAME | FILEHASH | CONTENT"
        elif self.include_time and not self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD HH:MM | SIZE(KB) | FILENAME | FILEHASH
            return "# YYYY.MM.DD HH:MM | SIZE(KB) | FILENAME | FILEHASH"
        elif self.include_time and not self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD HH:MM | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD HH:MM | FILENAME | FILEHASH | CONTENT"
        elif self.include_time and not self.include_depth and not self.include_size and not self.include_content:
            # #  YYYY.MM.DD HH:MM | FILENAME | FILEHASH
            return "# YYYY.MM.DD HH:MM | FILENAME | FILEHASH"
        elif not self.include_time and self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD | DEPTH | SIZE(KB) | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD | DEPTH | SIZE(KB) | FILENAME | FILEHASH | CONTENT"
        elif not self.include_time and self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD | DEPTH | SIZE(KB) | FILENAME | FILEHASH
            return "# YYYY.MM.DD | DEPTH | SIZE(KB) | FILENAME | FILEHASH"
        elif not self.include_time and self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD | DEPTH | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD | DEPTH | FILENAME | FILEHASH | CONTENT"
        elif not self.include_time and self.include_depth and not self.include_size and not self.include_content:
            # #  YYYY.MM.DD | DEPTH | FILENAME | FILEHASH
            return "# YYYY.MM.DD | DEPTH | FILENAME | FILEHASH"
        elif not self.include_time and not self.include_depth and self.include_size and self.include_content:
            # #  YYYY.MM.DD | SIZE(KB) | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD | SIZE(KB) | FILENAME | FILEHASH | CONTENT"
        elif not self.include_time and not self.include_depth and self.include_size and not self.include_content:
            # #  YYYY.MM.DD | SIZE(KB) | FILENAME | FILEHASH
            return "# YYYY.MM.DD | SIZE(KB) | FILENAME | FILEHASH"
        elif not self.include_time and not self.include_depth and not self.include_size and self.include_content:
            # #  YYYY.MM.DD | FILENAME | FILEHASH | CONTENT
            return "# YYYY.MM.DD | FILENAME | FILEHASH | CONTENT"
        else:
            # #  YYYY.MM.DD | FILENAME | FILEHASH
            return "# YYYY.MM.DD | FILENAME | FILEHASH"

    def _build_data_line(self, file_hash: str, filename: str, date_str: str, depth_str: str, size_str: str, content_str: str, max_length: int) -> str:
        """Return a single data line with the right columns for date/depth/size/filename/hash/content."""
        # Always triple single-quotes for date portion
        # Possibly also "depth" and/or "size" plus the triple quotes for the next chunk
        # Then the double-quoted filename, single-quoted hash, etc.

        padded_filename = f"\"{filename}\"".ljust(max_length)

        # Base line without content
        if self.include_depth and depth_str and self.include_size and size_str:
            base_line = f"''' {date_str} | {depth_str} | {size_str} | ''' - {padded_filename} # | '{file_hash}'"
        elif self.include_depth and depth_str and not self.include_size:
            base_line = f"''' {date_str} | {depth_str} | ''' - {padded_filename} # | '{file_hash}'"
        elif not self.include_depth and self.include_size and size_str:
            base_line = f"''' {date_str} | {size_str} | ''' - {padded_filename} # | '{file_hash}'"
        else:
            base_line = f"''' {date_str} | ''' - {padded_filename} # | '{file_hash}'"

        # Add content if needed
        if self.include_content and content_str:
            return f"{base_line} | ''' {content_str} '''\n"
        else:
            return f"{base_line}\n"

    def read(self) -> List[Tuple[str, str, str, str, str, str]]:
        """
        Reads lines in all possible formats, returning (hash, filename, date_str, depth_str, size_str, content_str).
        - It can read any format as long as filenames are in double quotes and hashes are in single quotes
        - The original formatting doesn't matter - columns can be reordered, added, or removed
        - The file can be completely restructured (as long as essential elements remain)
        - Only the hash and filename are required for the renaming operation to work.
        """
        hash_entries: List[Tuple[str, str, str, str, str, str]] = []

        try:
            with self.file_path.open("r", encoding='utf-8') as f:
                for line_num, line in enumerate(f):
                    if not line.strip() or line.strip().startswith("#"):
                        continue
                    entry = self._parse_hash_entry(line)
                    if entry:
                        hash_entries.append(entry)
        except IOError as error:
            logger.error(f"Failed to read hash file: {error}")

        return hash_entries

    def _parse_hash_entry(self, line: str) -> Optional[Tuple[str, str, str, str, str, str]]:
        """Extract essential hash and filename regardless of formatting."""
        line = line.strip()
        if not line or line.startswith("#"):
            return None

        # Extract content preview if present
        content_str = ""
        hash_pattern = r"'([a-fA-F0-9]{64})'"
        hash_match = re.search(hash_pattern, line)
        
        if hash_match:
            file_hash = hash_match.group(1)
            remainder = line[hash_match.end():].strip()
            
            # Look for content part (| ''' content ''')
            if remainder.startswith("|"):
                content_match = re.search(r"\|\s*'''\s*(.*?)\s*'''", remainder)
                if content_match:
                    content_str = content_match.group(1)
        
        # Extract the two essential elements: filename and hash
        filename_match = re.search(r'"([^"]+)"', line)
        hash_match = re.search(r"'([a-fA-F0-9]{64})'", line)
        
        if filename_match and hash_match:
            filename = filename_match.group(1)
            file_hash = hash_match.group(1)
            
            # Extract date if present - common format YYYY.MM.DD with optional HH:MM
            date_str = ""
            date_match = re.search(r"(\d{4}\.\d{2}\.\d{2}(?:\s+\d{2}:\d{2})?)", line)
            if date_match:
                date_str = date_match.group(1)
                
            # Extract depth if present - format lvl.N
            depth_str = ""
            depth_match = re.search(r"lvl\.(\d+)", line)
            if depth_match:
                depth_str = f"lvl.{depth_match.group(1)}"
                
            # Extract size if present - format NNN.kb
            size_str = ""
            size_match = re.search(r"(\d+\.kb)", line)
            if size_match:
                size_str = size_match.group(1)
                
            return (file_hash, filename, date_str, depth_str, size_str, content_str)
            
        logger.warning(f"Could not extract filename and hash from: {line}")
        return None


# =======================================================
# File Renamer
# =======================================================
class FileRenamer:
    """Handles the renaming of files based on hash comparisons."""

    def __init__(self, root_dir: pathlib.Path):
        self.root_dir = root_dir

    def execute(
        self,
        source_hashes: List[Tuple[str, str, str, str, str, str]],
        target_hashes: List[Tuple[str, str, str, str, str, str]],
        dry_run: bool = True
    ) -> bool:
        """
        Files are identified solely by their hash, with the desired filename representing its target name.

        NOTE: Only the hash and filename are needed for the renaming operation - all other fields
        (date, depth, size, content) are purely for presentation and can be freely modified by the user.

        Returns: True if there were errors during execution, False otherwise.
        """
        # Extract only the hash and filename for determining renames
        src_map_entries = [(h, f) for (h, f, _, _, _, _) in source_hashes]
        tgt_map_entries = [(h, f) for (h, f, _, _, _, _) in target_hashes]

        source_map = self._map_hash_to_paths(src_map_entries)
        target_map = self._map_hash_to_paths(tgt_map_entries)

        rename_pairs = self._determine_rename_pairs(source_map, target_map)
        conflicts = False
        errors = False

        if dry_run:
            self._preview_renames(rename_pairs)

        for src_rel, tgt_rel in rename_pairs:
            src_path = self.root_dir / src_rel
            tgt_path = self.root_dir / tgt_rel

            if src_rel == tgt_rel:
                logger.debug(f"Unchanged: {src_rel}")
                continue

            if not self._validate_paths(src_path, tgt_path):
                conflicts = True
                continue

            if dry_run:
                logger.info(f'Will rename: "{src_rel}" → "{tgt_rel}"')
            else:
                # Track if this rename operation had errors
                rename_had_errors = self._perform_rename(src_path, tgt_path, src_rel, tgt_rel)
                if rename_had_errors:
                    errors = True

        self._log_completion(dry_run, conflicts)
        # For dry runs, return False (no errors) since no actual operations occurred
        # For actual runs, return whether there were errors during file operations
        return errors if not dry_run else False

    def _map_hash_to_paths(self, hash_entries: List[Tuple[str, str]]) -> Dict[str, List[str]]:
        hash_map: Dict[str, List[str]] = {}
        for file_hash, path in hash_entries:
            hash_map.setdefault(file_hash, []).append(path)
        return hash_map

    def _determine_rename_pairs(
        self,
        source_map: Dict[str, List[str]],
        target_map: Dict[str, List[str]],
    ) -> List[Tuple[str, str]]:
        pairs: List[Tuple[str, str]] = []
        processed_targets: Set[str] = set()

        for file_hash, src_paths in source_map.items():
            tgt_paths = target_map.get(file_hash, [])
            for src in src_paths:
                if any(src == pair[0] for pair in pairs):
                    # Already handled
                    continue

                available_tgts = [t for t in tgt_paths if t not in processed_targets]
                if not available_tgts:
                    logger.warning(f"No matching hash for: {src}")
                    continue

                best_match = self._select_best_match(src, available_tgts)
                if best_match:
                    pairs.append((src, best_match))
                    processed_targets.add(best_match)

        return pairs

    def _select_best_match(self, source: str, targets: List[str]) -> Optional[str]:
        source_clean = self._clean_name(source)
        best_similarity = -1.0
        best_target = None

        for tgt in targets:
            tgt_clean = self._clean_name(tgt)
            similarity = self._name_similarity(source_clean, tgt_clean)
            if similarity > best_similarity:
                best_similarity = similarity
                best_target = tgt

        if best_target:
            logger.debug(f"Best match for '{source}' is '{best_target}' with similarity {best_similarity:.2f}")
        else:
            logger.warning(f"No suitable match found for '{source}'")
        return best_target

    @staticmethod
    def _name_similarity(name1: str, name2: str) -> float:
        matches = sum(a == b for a, b in zip(name1, name2))
        max_len = max(len(name1), len(name2))
        return matches / max_len if max_len else 0

    @staticmethod
    def _clean_name(name: str) -> str:
        # Normalize to ASCII, remove special chars
        name = unicodedata.normalize('NFKD', name).encode('ASCII', 'ignore').decode('ASCII')
        name = name.lower()
        name = pathlib.Path(name).stem
        name = re.sub(r'[^a-z0-9]', '', name)
        return name

    def _validate_paths(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        if not src.exists():
            logger.warning(f"Source missing: {src.relative_to(self.root_dir)}")
            return False
        if tgt.exists() and tgt != src:
            logger.error(f"Target exists: {tgt.relative_to(self.root_dir)}")
            return False

        return True

    def _sanitize_filename(self, path: pathlib.Path) -> pathlib.Path:
        """
        Sanitize a filename to make it safe for filesystem operations.
        Automatically fixes invalid filenames instead of rejecting them.
        """
        try:
            original_name = path.name
            name = path.stem
            suffix = path.suffix

            # Handle empty or whitespace-only names
            if not name or not name.strip():
                name = "unnamed_file"
                logger.info(f"Empty filename replaced with: {name}{suffix}")

            # Replace invalid characters with underscores
            invalid_chars = '<>:"|?*\x00'
            for char in invalid_chars:
                if char in name:
                    name = name.replace(char, '_')

            # Handle reserved Windows names by adding suffix
            reserved_names = {
                'CON', 'PRN', 'AUX', 'NUL',
                'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
                'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
            }
            if name.upper() in reserved_names:
                name = f"{name}_file"
                logger.info(f"Reserved name '{original_name}' changed to: {name}{suffix}")

            # Truncate if too long (leave room for suffix and potential numbering)
            max_name_length = 200 - len(suffix)
            if len(name.encode('utf-8')) > max_name_length:
                # Truncate to fit within limits
                while len(name.encode('utf-8')) > max_name_length and name:
                    name = name[:-1]
                logger.info(f"Long filename truncated to: {name}{suffix}")

            # Construct the sanitized path
            sanitized_name = f"{name}{suffix}"
            sanitized_path = path.parent / sanitized_name

            # If we made changes, log them
            if sanitized_name != original_name:
                logger.info(f"Filename sanitized: '{original_name}' → '{sanitized_name}'")

            return sanitized_path

        except Exception as e:
            logger.error(f"Error sanitizing filename {path}: {e}")
            # Fallback to a safe default
            fallback_name = f"sanitized_file_{hash(str(path)) % 10000}{path.suffix}"
            return path.parent / fallback_name

    def _perform_rename(self, src: pathlib.Path, tgt: pathlib.Path, src_rel: str, tgt_rel: str) -> bool:
        """
        Safely rename a file with automatic filename sanitization.
        Returns True if there was an error, False if successful.
        """
        try:
            # SAFETY: Sanitize the target filename to ensure it's valid
            sanitized_tgt = self._sanitize_filename(tgt)

            # Update the relative path if sanitization changed the filename
            if sanitized_tgt != tgt:
                sanitized_tgt_rel = str(sanitized_tgt.relative_to(self.root_dir))
            else:
                sanitized_tgt_rel = tgt_rel

            # Test the rename operation with the sanitized target
            if not self._test_rename_operation(src, sanitized_tgt):
                logger.error(f"Pre-validation failed for {src_rel} → {sanitized_tgt_rel}, skipping")
                return True  # Return True to indicate error

            # Now perform the actual rename
            sanitized_tgt.parent.mkdir(parents=True, exist_ok=True)
            shutil.move(str(src), str(sanitized_tgt))
            logger.info(f'Renamed: "{src_rel}" → "{sanitized_tgt_rel}"')
            return False  # Return False to indicate success
        except OSError as error:
            logger.error(f"Failed to rename {src_rel}: {error}")
            return True  # Return True to indicate error

    def _test_rename_operation(self, src: pathlib.Path, tgt: pathlib.Path) -> bool:
        """
        Test if a rename operation will succeed without actually performing it.
        Returns True if the operation should proceed, False if it should be skipped.
        """
        try:
            # Test if we can create the target directory
            tgt.parent.mkdir(parents=True, exist_ok=True)

            # Test if we can create a file in the target location
            test_file = tgt.with_suffix(tgt.suffix + '.test_temp')
            try:
                test_file.touch()
                test_file.unlink()  # Clean up immediately
                return True
            except OSError as e:
                logger.error(f"Cannot create file at target location {tgt}: {e}")
                return False

        except OSError as e:
            logger.error(f"Cannot create target directory for {tgt}: {e}")
            return False

    def _preview_renames(self, rename_pairs: List[Tuple[str, str]]) -> None:
        if not rename_pairs:
            logger.warning("No files require renaming")
            return

        table = Table(
            title="Pending Rename Operations",
            show_header=True,
            header_style="bold blue",
            box=ROUNDED
        )
        table.add_column("Operation", style="cyan", width=4)
        table.add_column("Source", style="white")
        table.add_column("Target", style="green")

        changes = 0
        for src, tgt in rename_pairs:
            # Apply sanitization to target filename for preview
            tgt_path = self.root_dir / tgt
            sanitized_tgt_path = self._sanitize_filename(tgt_path)
            sanitized_tgt = str(sanitized_tgt_path.relative_to(self.root_dir))

            if src != sanitized_tgt:
                table.add_row("→", src, sanitized_tgt)
                changes += 1

        if changes:
            console = Console()
            console.print("\n")
            console.print(Panel(table, border_style="blue"))
            console.print(f"\n[bold blue]Total pending changes:[/] [green]{changes}[/]\n")
        else:
            logger.warning("No files require renaming")

    def _log_completion(self, dry_run: bool, has_conflicts: bool) -> None:
        operation = "Dry run" if dry_run else "File renaming"
        if has_conflicts:
            logger.warning(f"{operation}: Conflicts detected, some files skipped")
        else:
            logger.info(f"{operation} completed successfully")


# =======================================================
# File Editor
# =======================================================
class FileEditor:
    """Opens files using the default system editor."""

    @staticmethod
    def open(file_path: pathlib.Path) -> None:
        try:
            if sys.platform.startswith('darwin'):
                subprocess.call(['open', str(file_path)])
            elif os.name == 'nt':
                os.startfile(str(file_path))
            elif os.name == 'posix':
                subprocess.call(['xdg-open', str(file_path)])
            else:
                logger.warning(f"Unsupported platform: {sys.platform}")
        except Exception as error:
            logger.error(f"Failed to open editor: {error}")


# =======================================================
# Text Minifier
# =======================================================
class TextMinifier:
    """Handles minification of text content into single-line representation."""

    @staticmethod
    def minify_text(text, max_length=None):
        """
        Converts multi-line text to a single line with escaped newlines.
        Optionally truncates to max_length.
        """
        # First escape any existing backslashes to avoid issues
        text = text.replace('\\', '\\\\')

        # Special handling for tabs and other control characters
        text = text.replace('\t', '\\t')
        text = text.replace('\r', '\\r')

        # Split the text into lines and join with escaped newlines
        lines = text.splitlines()
        result = '\\n'.join(lines)

        # Truncate if necessary
        if max_length and len(result) > max_length:
            result = result[:max_length-3] + '...'

        return result

    @staticmethod
    def unminify_text(text):
        """
        Converts a single line with escaped sequences back to multi-line text.
        """
        # Handle all standard escape sequences
        escape_chars = {
            '\\n': '\n',   # newline
            '\\r': '\r',   # carriage return
            '\\t': '\t',   # tab
            '\\b': '\b',   # backspace
            '\\f': '\f',   # form feed
            '\\"': '"',    # double quote
            "\\'": "'",    # single quote
            '\\\\': '\\',  # backslash
            '\\/': '/'     # forward slash
        }

        # Process the string character by character
        result = ""
        i = 0
        while i < len(text):
            # Check for escape sequences (2-char sequences)
            if i + 1 < len(text) and text[i] == '\\':
                escape_seq = text[i:i+2]
                if escape_seq in escape_chars:
                    result += escape_chars[escape_seq]
                    i += 2
                    continue
                # Handle unicode escape sequences \uXXXX
                elif i + 5 < len(text) and text[i:i+2] == '\\u':
                    try:
                        hex_val = text[i+2:i+6]
                        result += chr(int(hex_val, 16))
                        i += 6
                        continue
                    except (ValueError, IndexError):
                        # If not a valid unicode escape, treat as normal characters
                        pass

            # Normal character
            result += text[i]
            i += 1

        return result


# =======================================================
# Main App Class
# =======================================================
class BatchRenameApp:
    """
    Encapsulates the CLI workflow for batch renaming:
      1) Parse & prompt for Arguments
      2) Initialize Logging
      3) Collect & write .original_hashes.py & .new_hashes.py
      4) Edit & confirm renaming
      5) Cleanup
    """

    def __init__(self):
        self.arg_handler = ArgumentHandler()
        self.args: Optional[argparse.Namespace] = None

    def run(self) -> None:
        # 1) Parse & prompt
        self.args = self.arg_handler.get_arguments()
        self.args = self.arg_handler.prompt_for_missing_arguments(self.args)

        # 2) Initialize Logging
        LoggerSetup.initialize_logging(self.args.verbosity)

        # 3) Handle the main process
        success = False
        has_errors = False
        try:
            has_errors = self.handle_process_command()
            success = True
        except Exception as e:
            logger.error(f"Execution failed: {e}")
            has_errors = True

        # 4) Cleanup logs if needed, successful, and no errors occurred
        if success and not has_errors and self.args.cleanup_logs:
            logger.remove()  # remove Loguru handler to release file
            log_file = Path("app.log.yml")
            if log_file.exists():
                console = Console()
                try:
                    log_file.unlink()
                    console.print(f"[bold green]Log file {log_file} has been cleaned up.[/bold green]\n")
                except Exception as e:
                    console.print(f"[bold red]Failed to delete log file {log_file}: {e}[/bold red]\n")
        elif has_errors and self.args.cleanup_logs:
            console = Console()
            console.print(f"[yellow]Log file preserved due to errors during execution.[/yellow]\n")

    def handle_process_command(self) -> bool:
        """
        Handle the main process command.
        Returns True if there were errors during execution, False otherwise.
        """
        console = Console()
        root_dir = pathlib.Path(self.args.directory).resolve()

        org_file = root_dir / ".original_hashes.py"
        new_file = root_dir / ".new_hashes.py"
        has_errors = False

        try:
            processor = FileProcessor(
                root_dir,
                self.args.include_subdirs,
                include_time=self.args.include_time,
                include_depth=self.args.include_depth,
                include_size=self.args.include_size,
                include_content=self.args.include_content,
                content_length=self.args.content_length
            )
            initial_hashes = processor.collect_file_hashes()

            # Write to both "original" and "new" for editing
            for file_path in (org_file, new_file):
                manager = HashFileManager(
                    file_path,
                    include_time=self.args.include_time,
                    include_depth=self.args.include_depth,
                    include_size=self.args.include_size,
                    include_content=self.args.include_content
                )
                manager.write(initial_hashes)

            logger.info("Opening new hash file for editing...")
            FileEditor.open(new_file)

            # Create a more informative confirmation message
            console.print("\n[blue]Note:[/] You can freely reorganize the files by editing their names in the hash file.", highlight=False)
            console.print("[blue]Tip:[/] The format is flexible, only the filename in quotes and hash in single-quotes are essential.", highlight=False)

            if not Confirm.ask("\nShow preview of filename changes? [y/n]: ", default="y"):
                logger.warning("Operation cancelled by user")
                return False

            # Create HashFileManager instances without parameters for reading
            org_manager = HashFileManager(org_file)
            new_manager = HashFileManager(new_file)

            org_hashes = org_manager.read()
            new_hashes = new_manager.read()
            renamer = FileRenamer(root_dir)
            # Dry run first
            dry_run_errors = renamer.execute(org_hashes, new_hashes, dry_run=True)
            if not dry_run_errors:  # Only proceed if dry run had no errors
                if Confirm.ask("\nApply these changes? [y/n]: ", default="y"):
                    # Check if actual rename operation had errors
                    has_errors = renamer.execute(org_hashes, new_hashes, dry_run=False)
                else:
                    # User cancelled - this is not an error, it's successful completion
                    has_errors = False
            else:
                # Dry run had errors
                has_errors = True

        finally:
            # Cleanup: Attempt to delete the hash files
            for file_path in (org_file, new_file):
                try:
                    if file_path.exists():
                        file_path.unlink()
                        logger.info(f"Cleaned up: {file_path.name}")
                except OSError as error:
                    logger.warning(f"Cleanup failed for {file_path.name}: {error}")

        return has_errors


# =======================================================
# Execution entrypoint
# =======================================================
def main() -> None:
    """Main entry point of the utility."""
    app = BatchRenameApp()
    app.run()


if __name__ == "__main__":
    main()
