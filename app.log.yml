- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: .cursorignore'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: .cursorrules'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *compute_sha256
  lineno: 329
  message: 'Using fast hash for large file: 20250122_104934.wav (438.4MB)'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: 20250122_104934.wav'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: git_history_graph.md'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: py_venv_init.bat'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: py__RenameWithEditor.sublime-project'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: requirements.txt'

- time: 2025-06-13 13:00:36
  level: !DEBUG
  name: __main__
  funcName: *collect_file_hashes
  lineno: 504
  message: 'Processed: src.md'

- time: 2025-06-13 13:00:36
  level: !INFO
  name: __main__
  funcName: *write
  lineno: 615
  message: 'Hash file written: .original_hashes.py'

- time: 2025-06-13 13:00:36
  level: !INFO
  name: __main__
  funcName: *write
  lineno: 615
  message: 'Hash file written: .new_hashes.py'

- time: 2025-06-13 13:00:36
  level: !INFO
  name: __main__
  funcName: *handle_process_command
  lineno: 1240
  message: Opening new hash file for editing...

- time: 2025-06-13 13:00:58
  level: !WARNING
  name: __main__
  funcName: *_parse_hash_entry
  lineno: 767
  message: 'Could not extract filename and hash from: ''' 2025.01.25 19:16 | lvl.1 | 448872.kb | ''' - "20250122_104934.wav"                  # | 'FAST_6b84dc18c9b5103ee3fb499f365828044728e46930082654983442c50330a24a''

- time: 2025-06-13 13:00:58
  level: !WARNING
  name: __main__
  funcName: *_parse_hash_entry
  lineno: 767
  message: 'Could not extract filename and hash from: ''' 2025.01.25 19:16 | lvl.1 | 448872.kb | ''' - "2025.0122_104934.wav"                  # | 'FAST_6b84dc18c9b5103ee3fb499f365828044728e46930082654983442c50330a24a''

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for '.cursorignore' is '.cursorignore' with similarity 1.00

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for '.cursorrules' is '.cursorrules' with similarity 1.00

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'git_history_graph.md' is 'git_history_graph.md' with similarity 1.00

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'py__RenameWithEditor.sublime-project' is 'py__RenameWithEditor.sublime-project' with similarity 1.00

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'py_venv_init.bat' is 'py_venv_init.bat' with similarity 1.00

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'requirements.txt' is 'requirements.txt' with similarity 1.00

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'src.md' is 'src.md' with similarity 1.00

- time: 2025-06-13 13:00:58
  level: !WARNING
  name: __main__
  funcName: *_preview_renames
  lineno: 1048
  message: No files require renaming

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: .cursorignore'

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: .cursorrules'

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: git_history_graph.md'

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: py__RenameWithEditor.sublime-project'

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: py_venv_init.bat'

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: requirements.txt'

- time: 2025-06-13 13:00:58
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: src.md'

- time: 2025-06-13 13:00:58
  level: !INFO
  name: __main__
  funcName: *_log_completion
  lineno: 1055
  message: Dry run completed successfully

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for '.cursorignore' is '.cursorignore' with similarity 1.00

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for '.cursorrules' is '.cursorrules' with similarity 1.00

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'git_history_graph.md' is 'git_history_graph.md' with similarity 1.00

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'py__RenameWithEditor.sublime-project' is 'py__RenameWithEditor.sublime-project' with similarity 1.00

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'py_venv_init.bat' is 'py_venv_init.bat' with similarity 1.00

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'requirements.txt' is 'requirements.txt' with similarity 1.00

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *_select_best_match
  lineno: 879
  message: Best match for 'src.md' is 'src.md' with similarity 1.00

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: .cursorignore'

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: .cursorrules'

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: git_history_graph.md'

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: py__RenameWithEditor.sublime-project'

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: py_venv_init.bat'

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: requirements.txt'

- time: 2025-06-13 13:01:09
  level: !DEBUG
  name: __main__
  funcName: *execute
  lineno: 813
  message: 'Unchanged: src.md'

- time: 2025-06-13 13:01:09
  level: !INFO
  name: __main__
  funcName: *_log_completion
  lineno: 1055
  message: File renaming completed successfully

- time: 2025-06-13 13:01:09
  level: !INFO
  name: __main__
  funcName: *handle_process_command
  lineno: 1277
  message: 'Cleaned up: .original_hashes.py'

- time: 2025-06-13 13:01:09
  level: !INFO
  name: __main__
  funcName: *handle_process_command
  lineno: 1277
  message: 'Cleaned up: .new_hashes.py'

