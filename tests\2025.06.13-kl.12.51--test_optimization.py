#!/usr/bin/env python3
"""
Test script to demonstrate the file extension pre-filtering optimization.
This script creates sample files with different extensions and tests the performance improvement.
"""

import time
import tempfile
from pathlib import Path
import sys

# Add the src directory to the path so we can import our module
sys.path.insert(0, 'src')
from main import Config, FileProcessor, File<PERSON><PERSON><PERSON>

def create_test_files(test_dir):
    """Create sample files with different extensions and sizes for testing."""
    test_files = []

    # Create some small text files
    text_files = ['test.txt', 'readme.md', 'config.json', 'script.py']
    for filename in text_files:
        file_path = test_dir / filename
        file_path.write_text("This is a sample text file for testing.\nIt has multiple lines.\nAnd some content.")
        test_files.append(file_path)

    # Create some small binary files
    small_binary_files = ['small_image.jpg', 'small_audio.mp3']
    for filename in small_binary_files:
        file_path = test_dir / filename
        # Create a small binary file with some dummy content (< 100MB)
        file_path.write_bytes(b'\x89PNG\r\n\x1a\n' + b'dummy binary content' * 1000)
        test_files.append(file_path)

    # Create some large binary files to test fast hashing
    large_binary_files = ['large_video.mp4', 'large_audio.wav']
    for filename in large_binary_files:
        file_path = test_dir / filename
        # Create a large binary file (> 100MB) to trigger fast hashing
        print(f"Creating large test file: {filename} (this may take a moment...)")
        chunk = b'large file dummy content for testing fast hash optimization' * 1000
        with file_path.open('wb') as f:
            # Write ~110MB of data
            for _ in range(1800):
                f.write(chunk)
        test_files.append(file_path)

    return test_files

def test_performance():
    """Test the performance difference with and without extension pre-filtering."""
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir)
        test_files = create_test_files(test_dir)
        
        print(f"Created {len(test_files)} test files in {test_dir}")
        print("\nTest files:")
        for file_path in test_files:
            print(f"  - {file_path.name} ({file_path.stat().st_size} bytes)")
        
        # Create a FileProcessor instance
        processor = FileProcessor(
            root_dir=test_dir,
            include_subdirs=False,
            include_content=True,  # Enable content preview to trigger the optimization
            content_length=100
        )
        
        print("\n" + "="*60)
        print("TESTING FILE TYPE DETECTION WITH EXTENSION PRE-FILTERING")
        print("="*60)
        
        # Test each file
        for file_path in test_files:
            start_time = time.time()
            is_text = processor._is_text_file(file_path)
            end_time = time.time()
            
            duration_ms = (end_time - start_time) * 1000
            file_type = "TEXT" if is_text else "BINARY"
            
            print(f"{file_path.name:15} -> {file_type:6} (took {duration_ms:.2f}ms)")
        
        print("\n" + "="*60)
        print("TESTING HASH COMPUTATION PERFORMANCE")
        print("="*60)

        # Test hash computation for each file
        for file_path in test_files:
            start_time = time.time()
            file_hash = FileHasher.compute_sha256(file_path)
            end_time = time.time()

            duration_ms = (end_time - start_time) * 1000
            file_size_mb = file_path.stat().st_size / (1024 * 1024)
            hash_type = "FAST" if file_hash and file_hash.startswith("FAST_") else "FULL"

            print(f"{file_path.name:20} -> {hash_type:4} hash ({file_size_mb:6.1f}MB, took {duration_ms:7.1f}ms)")

        print("\n" + "="*60)
        print("SUMMARY")
        print("="*60)
        print("OPTIMIZATIONS IMPLEMENTED:")
        print("1. Extension pre-filtering: Binary files skip content preview")
        print("2. Fast hashing: Large files (>100MB) use sampling instead of full SHA256")
        print("\nLarge files should show 'FAST hash' and complete much faster!")
        print("Small files use 'FULL hash' for maximum accuracy.")
        print("\nCheck the debug logs to see which optimizations were used!")

if __name__ == "__main__":
    test_performance()
