# Tests for RenameWithEditor Utility

This directory contains comprehensive tests for the RenameWithEditor utility, focusing on the filename sanitization safety features that prevent file destruction.

## Test Structure

```
tests/
├── __init__.py                    # Tests package initialization
├── README.md                      # This file
├── run_tests.py                   # Test runner script
├── test_filename_sanitizer.py     # Unit tests for sanitization features
└── demo_sanitizer.py              # Interactive demonstration script
```

## Running Tests

### Run All Tests
```bash
python tests/run_tests.py
```

### Run Specific Test File
```bash
python -m unittest tests.test_filename_sanitizer
```

### Run Individual Test Class
```bash
python -m unittest tests.test_filename_sanitizer.TestFilenameSanitizer
```

### Run Interactive Demo
```bash
python tests/demo_sanitizer.py
```

## Test Coverage

### TestFilenameSanitizer
Tests the core filename sanitization functionality:
- **Empty filename handling**: Ensures empty filenames become "unnamed_file"
- **Reserved name protection**: Tests Windows reserved names (CON, PRN, etc.) get "_file" suffix
- **Invalid character replacement**: Verifies `<>:"|?*` characters are replaced with underscores
- **Long filename truncation**: Tests overly long filenames are truncated safely
- **Extension preservation**: Ensures file extensions are maintained during sanitization
- **Valid filename passthrough**: Confirms valid filenames remain unchanged

### TestPreviewSanitization
Tests the preview integration:
- **Preview accuracy**: Ensures preview shows sanitized filenames that will actually be used
- **No crashes**: Verifies preview handles problematic filenames gracefully

### TestRenameOperationSafety
Tests the safety mechanisms:
- **Pre-validation testing**: Tests the `_test_rename_operation` method
- **Source file protection**: Ensures source files are never destroyed during operations

## Safety Features Tested

1. **Filename Sanitization**
   - Automatic fixing of invalid filenames
   - Preservation of user intent while ensuring filesystem compatibility
   - Comprehensive handling of edge cases

2. **Preview Integration**
   - Transparent display of actual target filenames
   - User sees exactly what will happen before operations

3. **Operation Safety**
   - Pre-validation prevents risky operations
   - Source files are protected from destruction
   - Graceful handling of filesystem limitations

## Test Results

All tests should pass, indicating:
- ✅ No file destruction from invalid filenames
- ✅ Operations succeed instead of failing
- ✅ User-friendly automatic corrections
- ✅ Comprehensive safety for filesystem edge cases

## Adding New Tests

When adding new tests:
1. Follow the existing unittest pattern
2. Use descriptive test method names starting with `test_`
3. Include docstrings explaining what each test validates
4. Use `setUp()` and `tearDown()` for test isolation
5. Test both positive and negative cases

## Demo Script

The `demo_sanitizer.py` script provides an interactive demonstration of:
- How invalid filenames are automatically corrected
- Preview integration showing sanitized target filenames
- Real-world examples of the safety features in action

This is useful for understanding the functionality and demonstrating the safety improvements to users.
