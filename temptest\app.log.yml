- time: 2025-06-13 13:05:23
  level: !INFO
  name: __main__
  funcName: *write
  lineno: 615
  message: 'Hash file written: .original_hashes.py'

- time: 2025-06-13 13:05:23
  level: !INFO
  name: __main__
  funcName: *write
  lineno: 615
  message: 'Hash file written: .new_hashes.py'

- time: 2025-06-13 13:05:23
  level: !INFO
  name: __main__
  funcName: *handle_process_command
  lineno: 1240
  message: Opening new hash file for editing...

- time: 2025-06-13 13:05:44
  level: !WARNING
  name: __main__
  funcName: *_parse_hash_entry
  lineno: 767
  message: 'Could not extract filename and hash from: ''' 2025.01.25 19:16 | lvl.1 | 448872.kb | ''' - "20250122_104934.wav" # | 'FAST_6b84dc18c9b5103ee3fb499f365828044728e46930082654983442c50330a24a''

- time: 2025-06-13 13:05:44
  level: !WARNING
  name: __main__
  funcName: *_parse_hash_entry
  lineno: 767
  message: 'Could not extract filename and hash from: ''' 2025.01.25 19:16 | lvl.1 | 448872.kb | ''' - "2025.01.22-kl.10.49.34.wav" # | 'FAST_6b84dc18c9b5103ee3fb499f365828044728e46930082654983442c50330a24a''

- time: 2025-06-13 13:05:44
  level: !WARNING
  name: __main__
  funcName: *_preview_renames
  lineno: 1018
  message: No files require renaming

- time: 2025-06-13 13:05:44
  level: !INFO
  name: __main__
  funcName: *_log_completion
  lineno: 1055
  message: Dry run completed successfully

