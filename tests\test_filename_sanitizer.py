#!/usr/bin/env python3
"""
Unit tests for the filename sanitizer functionality in RenameWithEditor.
Tests the safety features that prevent file destruction through automatic
filename sanitization.
"""

import unittest
import tempfile
import pathlib
import sys
import os

# Add src to path to import the main module
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))
from main import FileRenamer


class TestFilenameSanitizer(unittest.TestCase):
    """Test cases for filename sanitization functionality."""

    def setUp(self):
        """Set up test environment with temporary directory."""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.temp_path = pathlib.Path(self.temp_dir.name)
        self.renamer = FileRenamer(self.temp_path)

    def tearDown(self):
        """Clean up test environment."""
        self.temp_dir.cleanup()

    def test_empty_filename_sanitization(self):
        """Test that empty filenames are replaced with 'unnamed_file'."""
        target_path = self.temp_path / ""
        # Use a space to represent empty filename for path creation
        target_path = self.temp_path / " "
        
        sanitized_path = self.renamer._sanitize_filename(target_path)
        self.assertEqual(sanitized_path.name, "unnamed_file")

    def test_reserved_names_sanitization(self):
        """Test that Windows reserved names get '_file' suffix."""
        reserved_names = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'LPT1']
        
        for reserved_name in reserved_names:
            with self.subTest(reserved_name=reserved_name):
                target_path = self.temp_path / f"{reserved_name}.txt"
                sanitized_path = self.renamer._sanitize_filename(target_path)
                expected_name = f"{reserved_name}_file.txt"
                self.assertEqual(sanitized_path.name, expected_name)

    def test_invalid_characters_sanitization(self):
        """Test that invalid characters are replaced with underscores."""
        invalid_chars = '<>:"|?*'
        
        for char in invalid_chars:
            with self.subTest(char=char):
                filename = f"file{char}name.txt"
                target_path = self.temp_path / filename
                sanitized_path = self.renamer._sanitize_filename(target_path)
                expected_name = "file_name.txt"
                self.assertEqual(sanitized_path.name, expected_name)

    def test_multiple_invalid_characters(self):
        """Test sanitization of filenames with multiple invalid characters."""
        target_path = self.temp_path / "file<>:|?*.txt"
        sanitized_path = self.renamer._sanitize_filename(target_path)
        expected_name = "file______.txt"
        self.assertEqual(sanitized_path.name, expected_name)

    def test_long_filename_truncation(self):
        """Test that overly long filenames are truncated."""
        long_name = "a" * 300 + ".txt"
        target_path = self.temp_path / long_name
        sanitized_path = self.renamer._sanitize_filename(target_path)
        
        # Should be truncated but still have the extension
        self.assertTrue(sanitized_path.name.endswith(".txt"))
        self.assertLess(len(sanitized_path.name.encode('utf-8')), 255)
        self.assertLess(len(sanitized_path.name), len(long_name))

    def test_valid_filename_unchanged(self):
        """Test that valid filenames pass through unchanged."""
        valid_names = [
            "normal_file.txt",
            "file-with-dashes.txt",
            "file_with_underscores.txt",
            "file.with.dots.txt",
            "file123.txt"
        ]
        
        for valid_name in valid_names:
            with self.subTest(valid_name=valid_name):
                target_path = self.temp_path / valid_name
                sanitized_path = self.renamer._sanitize_filename(target_path)
                self.assertEqual(sanitized_path.name, valid_name)

    def test_sanitization_preserves_extension(self):
        """Test that file extensions are preserved during sanitization."""
        test_cases = [
            ("CON.pdf", "CON_file.pdf"),
            ("file<name>.docx", "file_name_.docx"),
            ("file|name.jpg", "file_name.jpg")
        ]
        
        for original, expected in test_cases:
            with self.subTest(original=original):
                target_path = self.temp_path / original
                sanitized_path = self.renamer._sanitize_filename(target_path)
                self.assertEqual(sanitized_path.name, expected)

    def test_fallback_for_errors(self):
        """Test that the fallback mechanism works for unexpected errors."""
        # This is harder to test directly, but we can verify the method
        # doesn't crash on edge cases
        edge_cases = [
            self.temp_path / "\x00",  # Null character
            self.temp_path / "   ",   # Only whitespace
        ]
        
        for edge_case in edge_cases:
            with self.subTest(edge_case=str(edge_case)):
                try:
                    sanitized_path = self.renamer._sanitize_filename(edge_case)
                    # Should not crash and should return a valid path
                    self.assertIsInstance(sanitized_path, pathlib.Path)
                    self.assertTrue(len(sanitized_path.name) > 0)
                except Exception as e:
                    self.fail(f"Sanitizer crashed on edge case {edge_case}: {e}")


class TestPreviewSanitization(unittest.TestCase):
    """Test cases for preview integration with sanitization."""

    def setUp(self):
        """Set up test environment with temporary directory."""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.temp_path = pathlib.Path(self.temp_dir.name)
        self.renamer = FileRenamer(self.temp_path)

    def tearDown(self):
        """Clean up test environment."""
        self.temp_dir.cleanup()

    def test_preview_shows_sanitized_filenames(self):
        """Test that preview displays sanitized target filenames."""
        # Create test rename pairs with problematic target filenames
        test_rename_pairs = [
            ("normal_file.txt", "CON.txt"),
            ("another_file.txt", "file<with>bad:chars.txt"),
            ("third_file.txt", "valid_filename.txt"),
        ]
        
        # This test mainly ensures the preview method doesn't crash
        # and processes the sanitization correctly
        try:
            self.renamer._preview_renames(test_rename_pairs)
        except Exception as e:
            self.fail(f"Preview with sanitization failed: {e}")


class TestRenameOperationSafety(unittest.TestCase):
    """Test cases for safe rename operations."""

    def setUp(self):
        """Set up test environment with temporary directory and test files."""
        self.temp_dir = tempfile.TemporaryDirectory()
        self.temp_path = pathlib.Path(self.temp_dir.name)
        self.renamer = FileRenamer(self.temp_path)
        
        # Create a test file
        self.test_file = self.temp_path / "test_file.txt"
        self.test_file.write_text("This is a test file that should not be destroyed.")

    def tearDown(self):
        """Clean up test environment."""
        self.temp_dir.cleanup()

    def test_test_rename_operation(self):
        """Test the pre-validation rename operation testing."""
        # Test with a valid target
        valid_target = self.temp_path / "valid_target.txt"
        result = self.renamer._test_rename_operation(self.test_file, valid_target)
        self.assertTrue(result)

    def test_source_file_protection(self):
        """Test that source files are never destroyed during failed operations."""
        # Verify source file exists and has correct content
        self.assertTrue(self.test_file.exists())
        original_content = self.test_file.read_text()
        self.assertEqual(original_content, "This is a test file that should not be destroyed.")
        
        # Test various problematic scenarios
        problematic_targets = [
            self.temp_path / "CON.txt",
            self.temp_path / "file<bad>.txt",
            self.temp_path / ("a" * 300 + ".txt"),
        ]
        
        for target in problematic_targets:
            with self.subTest(target=str(target)):
                # The sanitizer should handle these, but verify source is safe
                sanitized_target = self.renamer._sanitize_filename(target)
                can_test = self.renamer._test_rename_operation(self.test_file, sanitized_target)
                
                # Regardless of the result, source file should be intact
                self.assertTrue(self.test_file.exists())
                current_content = self.test_file.read_text()
                self.assertEqual(current_content, original_content)


if __name__ == '__main__':
    unittest.main()
